{"name": "seeker-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.12.1", "@aws-sdk/client-s3": "^3.821.0", "@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/serve-static": "^5.0.3", "@types/aws-sdk": "^0.0.42", "argon2": "^0.43.0", "aws-sdk": "^2.1692.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dataloader": "^2.2.3", "date-fns": "^4.1.0", "express": "^5.1.0", "graphql": "^16.11.0", "joi": "^17.13.3", "lodash": "^4.17.21", "migrate-mongo": "^12.1.3", "mongoose": "^8.15.1", "mongoose-paginate-v2": "^1.9.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/cors": "^2.8.18", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/migrate-mongo": "^10.0.5", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}