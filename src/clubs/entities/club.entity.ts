import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.dto';

export enum ClubType {
  WEBSITE = 'website',
  ARTICLE = 'article',
  PROFILE = 'profile',
  MUSIC = 'music',
  VIDEO = 'video',
  BOOK = 'book',
}

registerEnumType(ClubType, { name: 'ClubType' });

@ObjectType()
@Schema()
export class Club extends MongooseSchema {
  @Field(() => String, { description: 'Club title for Open Graph' })
  @Prop({ required: true })
  title: string;

  @Field(() => String, { description: 'Club description for Open Graph' })
  @Prop({ required: true })
  description: string;

  @Field(() => [String], { description: 'Array of image URLs for Open Graph' })
  @Prop({ required: true, type: [String] })
  images: string[];

  @Field(() => String, { description: 'Club URL for Open Graph' })
  @Prop({ required: true })
  url: string;

  @Field(() => ClubType, { description: 'Club type for Open Graph' })
  @Prop({
    required: true,
    enum: Object.values(ClubType),
    default: ClubType.WEBSITE,
  })
  type: ClubType;
}

@ObjectType()
export class PaginatedClubs extends createPaginatedType(Club) {}

export const ClubSchema = SchemaFactory.createForClass(Club);
