import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.dto';
import { ClubCategory } from './club-categories.entity';
import mongoose from 'mongoose';

export enum ClubStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

registerEnumType(ClubStatus, { name: 'ClubStatus' });

@ObjectType()
export class Address {
  @Field(() => String, { description: 'Street address' })
  street: string;

  @Field(() => String, { description: 'City' })
  city: string;

  @Field(() => String, { description: 'State or province' })
  state: string;

  @Field(() => String, { description: 'Country' })
  country: string;

  @Field(() => String, { description: 'Postal code' })
  postalCode: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional address info',
  })
  additionalInfo?: string;
}

@ObjectType()
export class OpeningHours {
  @Field(() => String, { nullable: true, description: 'Monday hours' })
  monday?: string;

  @Field(() => String, { nullable: true, description: 'Tuesday hours' })
  tuesday?: string;

  @Field(() => String, { nullable: true, description: 'Wednesday hours' })
  wednesday?: string;

  @Field(() => String, { nullable: true, description: 'Thursday hours' })
  thursday?: string;

  @Field(() => String, { nullable: true, description: 'Friday hours' })
  friday?: string;

  @Field(() => String, { nullable: true, description: 'Saturday hours' })
  saturday?: string;

  @Field(() => String, { nullable: true, description: 'Sunday hours' })
  sunday?: string;
}

@ObjectType()
@Schema()
export class Club extends MongooseSchema {
  @Field(() => String, { description: 'Club name' })
  @Prop({ required: true })
  name: string;

  @Field(() => String, { description: 'Club description' })
  @Prop({ required: true })
  description: string;

  @Field(() => ClubCategory, { description: 'Club category' })
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  category: string;

  @Field(() => ClubStatus, { description: 'Club status' })
  @Prop({
    required: true,
    enum: Object.values(ClubStatus),
    default: ClubStatus.PENDING,
  })
  status: ClubStatus;

  @Field(() => String, { nullable: true, description: 'Club logo URL' })
  @Prop()
  logo?: string;

  @Field(() => String, { nullable: true, description: 'Club cover image URL' })
  @Prop()
  coverImage?: string;

  @Field(() => [String], { description: 'Array of club image URLs' })
  @Prop({ required: true, type: [String] })
  images: string[];

  @Field(() => String, { nullable: true, description: 'Club phone number' })
  @Prop()
  phone?: string;

  @Field(() => Address, { nullable: true, description: 'Club address' })
  @Prop({ type: Object })
  address?: Address;

  @Field(() => OpeningHours, { nullable: true, description: 'Opening hours' })
  @Prop({ type: Object })
  openingHours?: OpeningHours;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Field(() => Boolean, { description: 'Whether club is featured' })
  @Prop({ default: false })
  featured: boolean;

  @Field(() => Number, { description: 'Club rating (0-5)' })
  @Prop({ default: 0, min: 0, max: 5 })
  rating: number;
}

@ObjectType()
export class PaginatedClubs extends createPaginatedType(Club) {}

export const ClubSchema = SchemaFactory.createForClass(Club);
