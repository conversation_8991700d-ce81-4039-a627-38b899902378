import { ObjectType, Field, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.dto';
import { ClubCategory } from './club-categories.entity';
import mongoose from 'mongoose';
import { Contact } from 'src/users/entities/user.entity';

export enum ClubStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

registerEnumType(ClubStatus, { name: 'ClubStatus' });

@ObjectType()
export class Coordinates {
  @Field(() => Number, { description: 'Latitude' })
  latitude: number;

  @Field(() => Number, { description: 'Longitude' })
  longitude: number;
}

@ObjectType()
export class Address {
  @Field(() => String, { description: 'address' })
  @Prop({ required: true })
  address: string;

  @Field(() => Coordinates, { description: 'Coordinates' })
  @Prop({ required: true, type: Object })
  coordinates: Coordinates;

  @Field(() => String, { nullable: true, description: 'Metro line' })
  @Prop()
  metroLine?: string;

  // optional metro station
  @Field(() => String, { nullable: true, description: 'Metro station' })
  @Prop()
  metroStation?: string;
}

export enum DayOfWeek {
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
  SUNDAY = 'SUNDAY',
}

registerEnumType(DayOfWeek, { name: 'DayOfWeek' });

@ObjectType()
export class DayTiming {
  @Field(() => DayOfWeek, { description: 'Day of the week' })
  day: DayOfWeek;

  @Field(() => [Number], {
    description:
      'Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30)',
  })
  timings: [number, number];
}

@ObjectType()
export class OpeningHours {
  @Field(() => [DayTiming], {
    description:
      'Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format',
    defaultValue: [],
  })
  schedule: DayTiming[];
}

@ObjectType()
@Schema()
export class Club extends MongooseSchema {
  @Field(() => String, { description: 'Club name' })
  @Prop({ required: true })
  name: string;

  @Field(() => String, { description: 'Club description' })
  @Prop({ required: true })
  description: string;

  @Field(() => [ClubCategory], { description: 'Club category' })
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  categories: [string];

  @Field(() => ClubStatus, { description: 'Club status' })
  @Prop({
    required: true,
    enum: Object.values(ClubStatus),
    default: ClubStatus.PENDING,
  })
  status: ClubStatus;

  @Field(() => String, { nullable: true, description: 'Club logo URL' })
  @Prop()
  logo?: string;

  @Field(() => String, { nullable: true, description: 'Club cover image URL' })
  @Prop()
  coverImage?: string;

  @Field(() => [String], { description: 'Array of club image URLs' })
  @Prop({ required: true, type: [String] })
  images: string[];

  @Field(() => Contact, { nullable: true, description: 'Club phone number' })
  @Prop({ type: Object })
  phone?: Contact;

  @Field(() => Address, { nullable: true, description: 'Club address' })
  @Prop({ type: Object })
  address?: Address;

  @Field(() => OpeningHours, { nullable: true, description: 'Opening hours' })
  @Prop({ type: Object })
  openingHours?: OpeningHours;

  @Field(() => Boolean, { description: 'Whether club is featured' })
  @Prop({ default: false })
  featured: boolean;

  @Field(() => Number, { description: 'Club rating (0-5)' })
  @Prop({ default: 0, min: 0, max: 5 })
  rating: number;
}

@ObjectType()
export class PaginatedClubs extends createPaginatedType(Club) {}

export const ClubSchema = SchemaFactory.createForClass(Club);
