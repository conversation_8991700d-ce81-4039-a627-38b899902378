import {
  Resolver,
  Query,
  Mutation,
  Args,
  InputType,
  Field,
} from '@nestjs/graphql';
import { IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ClubsService } from './clubs.service';
import { Club, PaginatedClubs } from './entities/club.entity';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';
import { PaginationInput } from 'src/common/pagination.dto';

@InputType()
export class ClubsInput extends PaginationInput {
  @Field(() => PaginationInput, {
    nullable: true,
    description: 'Pagination options',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}

@Resolver(() => Club)
export class ClubsResolver {
  constructor(private readonly clubsService: ClubsService) {}

  @Mutation(() => Club)
  createClub(@Args('createClubInput') createClubInput: CreateClubInput) {
    return this.clubsService.create(createClubInput);
  }

  @Query(() => PaginatedClubs, { name: 'clubs' })
  findAll(@Args('clubsInput', { nullable: true }) clubsInput?: ClubsInput) {
    return this.clubsService.findAll({}, clubsInput?.pagination);
  }

  @Query(() => Club, { name: 'club' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.clubsService.findOne(id);
  }

  @Mutation(() => Club)
  updateClub(@Args('updateClubInput') updateClubInput: UpdateClubInput) {
    return this.clubsService.update(updateClubInput.id, updateClubInput);
  }

  @Mutation(() => Club)
  removeClub(@Args('id', { type: () => String }) id: string) {
    return this.clubsService.remove(id);
  }
}
