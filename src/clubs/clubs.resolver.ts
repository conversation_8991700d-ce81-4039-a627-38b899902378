import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ClubsService } from './clubs.service';
import { Club } from './entities/club.entity';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';

@Resolver(() => Club)
export class ClubsResolver {
  constructor(private readonly clubsService: ClubsService) {}

  @Mutation(() => Club)
  createClub(@Args('createClubInput') createClubInput: CreateClubInput) {
    return this.clubsService.create(createClubInput);
  }

  @Query(() => [Club], { name: 'clubs' })
  findAll() {
    return this.clubsService.findAll();
  }

  @Query(() => Club, { name: 'club' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.clubsService.findOne(id);
  }

  @Mutation(() => Club)
  updateClub(@Args('updateClubInput') updateClubInput: UpdateClubInput) {
    return this.clubsService.update(updateClubInput.id, updateClubInput);
  }

  @Mutation(() => Club)
  removeClub(@Args('id', { type: () => Int }) id: number) {
    return this.clubsService.remove(id);
  }
}
