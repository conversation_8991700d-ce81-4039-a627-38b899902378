import { InputType, Field } from '@nestjs/graphql';
import {
  IsString,
  IsArray,
  IsEnum,
  IsUrl,
  IsEmail,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsDateString,
  ValidateNested,
  ArrayMinSize,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ClubCategory, ClubStatus } from '../entities/club.entity';

@InputType()
export class AddressInput {
  @Field(() => String, { description: 'Street address' })
  @IsString()
  street: string;

  @Field(() => String, { description: 'City' })
  @IsString()
  city: string;

  @Field(() => String, { description: 'State or province' })
  @IsString()
  state: string;

  @Field(() => String, { description: 'Country' })
  @IsString()
  country: string;

  @Field(() => String, { description: 'Postal code' })
  @IsString()
  postalCode: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional address info',
  })
  @IsOptional()
  @IsString()
  additionalInfo?: string;
}

@InputType()
export class SocialMediaInput {
  @Field(() => String, { nullable: true, description: 'Facebook URL' })
  @IsOptional()
  @IsUrl()
  facebook?: string;

  @Field(() => String, { nullable: true, description: 'Instagram URL' })
  @IsOptional()
  @IsUrl()
  instagram?: string;

  @Field(() => String, { nullable: true, description: 'Twitter URL' })
  @IsOptional()
  @IsUrl()
  twitter?: string;

  @Field(() => String, { nullable: true, description: 'LinkedIn URL' })
  @IsOptional()
  @IsUrl()
  linkedin?: string;

  @Field(() => String, { nullable: true, description: 'YouTube URL' })
  @IsOptional()
  @IsUrl()
  youtube?: string;

  @Field(() => String, { nullable: true, description: 'TikTok URL' })
  @IsOptional()
  @IsUrl()
  tiktok?: string;
}

@InputType()
export class OpeningHoursInput {
  @Field(() => String, { nullable: true, description: 'Monday hours' })
  @IsOptional()
  @IsString()
  monday?: string;

  @Field(() => String, { nullable: true, description: 'Tuesday hours' })
  @IsOptional()
  @IsString()
  tuesday?: string;

  @Field(() => String, { nullable: true, description: 'Wednesday hours' })
  @IsOptional()
  @IsString()
  wednesday?: string;

  @Field(() => String, { nullable: true, description: 'Thursday hours' })
  @IsOptional()
  @IsString()
  thursday?: string;

  @Field(() => String, { nullable: true, description: 'Friday hours' })
  @IsOptional()
  @IsString()
  friday?: string;

  @Field(() => String, { nullable: true, description: 'Saturday hours' })
  @IsOptional()
  @IsString()
  saturday?: string;

  @Field(() => String, { nullable: true, description: 'Sunday hours' })
  @IsOptional()
  @IsString()
  sunday?: string;
}

@InputType()
export class CreateClubInput {
  @Field(() => String, { description: 'Club name' })
  @IsString()
  name: string;

  @Field(() => String, { description: 'Club description' })
  @IsString()
  description: string;

  @Field(() => ClubCategory, { description: 'Club category' })
  @IsEnum(ClubCategory)
  category: ClubCategory;

  @Field(() => ClubStatus, {
    description: 'Club status',
    defaultValue: ClubStatus.PENDING,
  })
  @IsEnum(ClubStatus)
  status: ClubStatus;

  @Field(() => String, { nullable: true, description: 'Club logo URL' })
  @IsOptional()
  @IsUrl()
  logo?: string;

  @Field(() => String, { nullable: true, description: 'Club cover image URL' })
  @IsOptional()
  @IsUrl()
  coverImage?: string;

  @Field(() => [String], { description: 'Array of club image URLs' })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one image URL is required' })
  @IsUrl({}, { each: true, message: 'Each image must be a valid URL' })
  images: string[];

  @Field(() => String, { nullable: true, description: 'Club email' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field(() => String, { nullable: true, description: 'Club phone number' })
  @IsOptional()
  @IsString()
  phone?: string;

  @Field(() => String, { nullable: true, description: 'Club website URL' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @Field(() => AddressInput, { nullable: true, description: 'Club address' })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInput)
  address?: AddressInput;

  @Field(() => SocialMediaInput, {
    nullable: true,
    description: 'Social media links',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaInput)
  socialMedia?: SocialMediaInput;

  @Field(() => OpeningHoursInput, {
    nullable: true,
    description: 'Opening hours',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OpeningHoursInput)
  openingHours?: OpeningHoursInput;

  @Field(() => Number, { nullable: true, description: 'Club capacity' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  capacity?: number;

  @Field(() => String, {
    nullable: true,
    description: 'Date established (ISO string)',
  })
  @IsOptional()
  @IsDateString()
  establishedDate?: string;

  @Field(() => [String], {
    description: 'Club tags for search and categorization',
    defaultValue: [],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @Field(() => Boolean, {
    description: 'Whether club is featured',
    defaultValue: false,
  })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @Field(() => Boolean, {
    description: 'Whether club is verified',
    defaultValue: false,
  })
  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @Field(() => Number, {
    description: 'Club rating (0-5)',
    defaultValue: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  rating?: number;

  @Field(() => Number, {
    description: 'Number of reviews',
    defaultValue: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  reviewCount?: number;
}
