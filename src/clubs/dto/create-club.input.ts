import { InputType, Field } from '@nestjs/graphql';
import {
  IsString,
  IsArray,
  IsEnum,
  IsUrl,
  IsEmail,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsDateString,
  ValidateNested,
  ArrayMinSize,
  Min,
  Max,
  ArrayMaxSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ClubStatus, DayOfWeek } from '../entities/club.entity';
import { ClubCategory } from '../entities/club-categories.entity';

@InputType()
export class AddressInput {
  @Field(() => String, { description: 'Street address' })
  @IsString()
  street: string;

  @Field(() => String, { description: 'City' })
  @IsString()
  city: string;

  @Field(() => String, { description: 'State or province' })
  @IsString()
  state: string;

  @Field(() => String, { description: 'Country' })
  @IsString()
  country: string;

  @Field(() => String, { description: 'Postal code' })
  @IsString()
  postalCode: string;

  @Field(() => String, {
    nullable: true,
    description: 'Additional address info',
  })
  @IsOptional()
  @IsString()
  additionalInfo?: string;
}

@InputType()
export class SocialMediaInput {
  @Field(() => String, { nullable: true, description: 'Facebook URL' })
  @IsOptional()
  @IsUrl()
  facebook?: string;

  @Field(() => String, { nullable: true, description: 'Instagram URL' })
  @IsOptional()
  @IsUrl()
  instagram?: string;

  @Field(() => String, { nullable: true, description: 'Twitter URL' })
  @IsOptional()
  @IsUrl()
  twitter?: string;

  @Field(() => String, { nullable: true, description: 'LinkedIn URL' })
  @IsOptional()
  @IsUrl()
  linkedin?: string;

  @Field(() => String, { nullable: true, description: 'YouTube URL' })
  @IsOptional()
  @IsUrl()
  youtube?: string;

  @Field(() => String, { nullable: true, description: 'TikTok URL' })
  @IsOptional()
  @IsUrl()
  tiktok?: string;
}

@InputType()
export class DayTimingInput {
  @Field(() => DayOfWeek, { description: 'Day of the week' })
  @IsEnum(DayOfWeek)
  day: DayOfWeek;

  @Field(() => [Number], {
    description:
      'Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30)',
  })
  @IsArray()
  @ArrayMinSize(1, {
    message: 'At least one timing (opening time) is required',
  })
  @ArrayMaxSize(2, {
    message: 'Maximum two timings allowed (opening and closing time)',
  })
  @IsNumber(
    {},
    { each: true, message: 'Each timing must be a number in HHMM format' },
  )
  @Min(0, { each: true, message: 'Time must be between 0000 and 2359' })
  @Max(2359, { each: true, message: 'Time must be between 0000 and 2359' })
  timings: number[];
}

@InputType()
export class OpeningHoursInput {
  @Field(() => [DayTimingInput], {
    description:
      'Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format',
    defaultValue: [],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DayTimingInput)
  schedule?: DayTimingInput[];
}

@InputType()
export class CreateClubInput {
  @Field(() => String, { description: 'Club name' })
  @IsString()
  name: string;

  @Field(() => String, { description: 'Club description' })
  @IsString()
  description: string;

  @Field(() => [ClubCategory], { description: 'Club category' })
  categories: ClubCategory;

  @Field(() => ClubStatus, {
    description: 'Club status',
    defaultValue: ClubStatus.PENDING,
  })
  @IsEnum(ClubStatus)
  status: ClubStatus;

  @Field(() => String, { nullable: true, description: 'Club logo URL' })
  @IsOptional()
  @IsUrl()
  logo?: string;

  @Field(() => String, { nullable: true, description: 'Club cover image URL' })
  @IsOptional()
  @IsUrl()
  coverImage?: string;

  @Field(() => [String], { description: 'Array of club image URLs' })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one image URL is required' })
  @IsUrl({}, { each: true, message: 'Each image must be a valid URL' })
  images: string[];

  @Field(() => String, { nullable: true, description: 'Club email' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field(() => String, { nullable: true, description: 'Club phone number' })
  @IsOptional()
  @IsString()
  phone?: string;

  @Field(() => String, { nullable: true, description: 'Club website URL' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @Field(() => AddressInput, { nullable: true, description: 'Club address' })
  @IsOptional()
  @ValidateNested()
  @Type(() => AddressInput)
  address?: AddressInput;

  @Field(() => SocialMediaInput, {
    nullable: true,
    description: 'Social media links',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SocialMediaInput)
  socialMedia?: SocialMediaInput;

  @Field(() => OpeningHoursInput, {
    nullable: true,
    description: 'Opening hours',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => OpeningHoursInput)
  openingHours?: OpeningHoursInput;

  @Field(() => Number, { nullable: true, description: 'Club capacity' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  capacity?: number;

  @Field(() => String, {
    nullable: true,
    description: 'Date established (ISO string)',
  })
  @IsOptional()
  @IsDateString()
  establishedDate?: string;

  @Field(() => [String], {
    description: 'Club tags for search and categorization',
    defaultValue: [],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @Field(() => Boolean, {
    description: 'Whether club is featured',
    defaultValue: false,
  })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @Field(() => Boolean, {
    description: 'Whether club is verified',
    defaultValue: false,
  })
  @IsOptional()
  @IsBoolean()
  verified?: boolean;

  @Field(() => Number, {
    description: 'Club rating (0-5)',
    defaultValue: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  rating?: number;

  @Field(() => Number, {
    description: 'Number of reviews',
    defaultValue: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  reviewCount?: number;
}
