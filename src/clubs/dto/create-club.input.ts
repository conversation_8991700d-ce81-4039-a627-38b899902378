import { InputType, Field } from '@nestjs/graphql';
import {
  IsString,
  IsArray,
  IsEnum,
  IsUrl,
  ArrayMinSize,
} from 'class-validator';
import { ClubType } from '../entities/club.entity';

@InputType()
export class CreateClubInput {
  @Field(() => String, { description: 'Club title for Open Graph' })
  @IsString()
  title: string;

  @Field(() => String, { description: 'Club description for Open Graph' })
  @IsString()
  description: string;

  @Field(() => [String], { description: 'Array of image URLs for Open Graph' })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one image URL is required' })
  @IsUrl({}, { each: true, message: 'Each image must be a valid URL' })
  images: string[];

  @Field(() => String, { description: 'Club URL for Open Graph' })
  @IsString()
  @IsUrl({}, { message: 'URL must be a valid URL' })
  url: string;

  @Field(() => ClubType, {
    description: 'Club type for Open Graph',
    defaultValue: ClubType.WEBSITE,
  })
  @IsEnum(ClubType)
  type: ClubType;
}
