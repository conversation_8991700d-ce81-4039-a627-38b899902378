import { Injectable } from '@nestjs/common';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';

@Injectable()
export class ClubsService {
  create(createClubInput: CreateClubInput) {
    return 'This action adds a new club';
  }

  findAll() {
    return `This action returns all clubs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} club`;
  }

  update(id: number, updateClubInput: UpdateClubInput) {
    return `This action updates a #${id} club`;
  }

  remove(id: number) {
    return `This action removes a #${id} club`;
  }
}
