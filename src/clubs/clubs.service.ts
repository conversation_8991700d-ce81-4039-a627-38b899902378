import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';
import { Club } from './entities/club.entity';
import { PaginationInput } from 'src/common/pagination.dto';

@Injectable()
export class ClubsService {
  constructor(
    @InjectModel(Club.name)
    private club: PaginateModel<Club>,
  ) {}

  async create(createClubInput: CreateClubInput) {
    // Convert establishedDate string to Date if provided
    const clubData = {
      ...createClubInput,
      establishedDate: createClubInput.establishedDate
        ? new Date(createClubInput.establishedDate)
        : undefined,
    };

    return this.club.create(clubData);
  }

  async findAll(
    filter: FilterQuery<Club> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.club.paginate(filter, paginationInput);
  }

  async findOne(id: string) {
    return this.club.findById(id);
  }

  async update(id: string, updateClubInput: UpdateClubInput) {
    // Convert establishedDate string to Date if provided
    const updateData = {
      ...updateClubInput,
      establishedDate: updateClubInput.establishedDate
        ? new Date(updateClubInput.establishedDate)
        : undefined,
    };

    // Remove the id from updateData to avoid updating it
    delete updateData.id;

    return this.club.findByIdAndUpdate(id, updateData, { new: true });
  }

  async remove(id: string) {
    return this.club.findByIdAndDelete(id);
  }
}
