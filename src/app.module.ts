/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { ApolloServerPluginInlineTrace } from '@apollo/server/plugin/inlineTrace';
import { ApolloServerPluginLandingPageLocalDefault } from '@apollo/server/plugin/landingPage/default';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { GraphQLModule } from '@nestjs/graphql';
import { JwtService } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import * as Joi from 'joi';
import mongoose from 'mongoose';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { AwsModule } from './aws/aws.module';
import { UsersModule } from './users/users.module';
import { AuthGuard } from './auth/guards/auth.guard';
import { RolesGuard } from './auth/guards/roles.guard';
import { MigrationService } from './migration/migration.service';
import { User } from './users/entities/user.entity';
import { Request } from 'express';
import { ClubsModule } from './clubs/clubs.module';
import * as mongoosePaginate from 'mongoose-paginate-v2';

const validationSchema = Joi.object({
  DATABASE_URI: Joi.string().required(),
  DATABASE_NAME: Joi.string().required(),
  JWT_SECRET: Joi.string().required(),
  MAX_UPLOAD_FILE_SIZE_IN_MB: Joi.number().required(),
  AWS_ACCESS_KEY_ID: Joi.string().required(),
  AWS_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_BUCKET: Joi.string().required(),
  AWS_BUCKET_URL: Joi.string().required(),
  AWS_REKOGNITION_COLLECTION_ID: Joi.string().required(),
  AWS_REGION: Joi.string().default('ap-southeast-1'),
});

export type GqlContext = {
  req: {
    user: User;
  } & Request;
};

@Module({
  imports: [
    ConfigModule.forRoot({ validationSchema }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => {
        // Add timestamps plugin
        mongoose.plugin((schema) => schema.set('timestamps', true));

        // paginate all documents
        mongoose.plugin(mongoosePaginate);

        // Check for existing connection
        if (global['mongooseConnection']) {
          console.log('Using existing mongoose connection');
          return {
            uri: config.get('DATABASE_URI'),
            connectionFactory: () => global['mongooseConnection'],
          };
        }

        // Create new connection
        console.log('Creating new mongoose connection');
        const uri = config.get('DATABASE_URI');
        const dbName = config.get('DATABASE_NAME');

        const connection = await mongoose.connect(uri, {
          dbName,
          // Add recommended settings for Lambda
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
        });

        // Cache the connection
        global['mongooseConnection'] = connection;

        return {
          uri,
          connectionFactory: () => connection,
        };
      },
      inject: [ConfigService],
    }),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,

      useFactory() {
        return {
          autoSchemaFile: {
            path: join(process.cwd(), 'src/schema.gql'),
          },
          introspection: true,
          playground: false,
          plugins: [
            ApolloServerPluginLandingPageLocalDefault() as any,
            ApolloServerPluginInlineTrace(),
          ],
          context() {
            return {};
          },
        };
      },
    }),

    AuthModule,
    UsersModule,
    AwsModule,
    ClubsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    JwtService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    MigrationService,
  ],
})
export class AppModule {}
