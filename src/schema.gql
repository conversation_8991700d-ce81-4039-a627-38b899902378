# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Contact {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

type User {
  _id: ID!
  id: ID!
  createdAt: DateTime!
  updatedAt: DateTime!

  """user fullname"""
  fullname: String!

  """user's email"""
  email: String!

  """user active status"""
  userStatus: UserStatus!

  """user role"""
  role: UserRoles!

  """User contact information"""
  contact: Contact!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  USER
}

type PaginatedUsers {
  """Array of documents"""
  docs: [User!]!

  """Total number of documents"""
  totalDocs: Int!

  """Number of documents per page"""
  limit: Int!

  """Current page number"""
  page: Int!

  """Total number of pages"""
  totalPages: Int!

  """Previous page number"""
  prevPage: Int

  """Next page number"""
  nextPage: Int

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Int
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type Club {
  """Example field (placeholder)"""
  exampleField: Int!
}

type Query {
  """Logged in  user"""
  me: User!
  users(usersInput: UsersInput): PaginatedUsers!
  user(id: String!): User!
  clubs: [Club!]!
  club(id: Int!): Club!
}

input UsersInput {
  """Page number (1-based)"""
  page: Int = 1

  """Number of items per page"""
  limit: Int = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!] = {}

  """Pagination options"""
  pagination: PaginationInput
}

input SortConfigInput {
  """Field to sort by"""
  field: String!

  """
  Sort order: "asc" or "desc"
  """
  order: String! = "desc"
}

input PaginationInput {
  """Page number (1-based)"""
  page: Int = 1

  """Number of items per page"""
  limit: Int = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!] = {}
}

type Mutation {
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  createClub(createClubInput: CreateClubInput!): Club!
  updateClub(updateClubInput: UpdateClubInput!): Club!
  removeClub(id: Int!): Club!
}

input SignInInput {
  """user phone number"""
  email: String!

  """user password"""
  password: String!
}

input CreateUserInput {
  """user fullname"""
  fullname: String!

  """user phone number"""
  email: String!

  """user role"""
  role: UserRoles!

  """user password"""
  password: String!

  """user active status"""
  userStatus: UserStatus!

  """User contact information"""
  contact: ContactInput!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

input ContactInput {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input CreateClubInput {
  """Example field (placeholder)"""
  exampleField: Int!
}

input UpdateClubInput {
  """Example field (placeholder)"""
  exampleField: Int
  id: Int!
}